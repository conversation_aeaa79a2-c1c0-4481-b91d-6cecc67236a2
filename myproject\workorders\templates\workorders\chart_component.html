{% load static %}
<script src="{% static 'workorders/js/activity-pagination.js' %}"></script>
<script src="{% static 'workorders/js/video-player.js' %}"></script>
<script src="{% static 'workorders/js/video-comment-manager.js' %}"></script>
<script src="{% static 'workorders/js/weather-history-chart.js' %}"></script>
<script src="{% static 'workorders/js/gps-data.js' %}"></script>

<!-- TomSelect CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.css" rel="stylesheet">
<link href="{% static 'workorders/css/tomselect-theme.css' %}" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>



<div class="flex flex-col gap-4 h-full">
    {% if aerotask_chart %}
    <div class="p-4 bg-white rounded-lg shadow-lg">
        <div class="overflow-x-auto w-full max-w-full">
            {{ aerotask_chart|safe }}
        </div>



        {% if workorder_id %}
        <!-- Tab Navigation -->
        <div class="mt-6 mb-4" x-data="{ activeTab: 'performance' }">
        <div class="mt-6 mb-4" x-data="{ activeTab: 'performance' }">
            <nav class="-mb-px flex space-x-8">
                <button @click="activeTab = 'performance'; $nextTick(() => { if (window.ActivityPerformance) window.ActivityPerformance.init(); })"
                        :class="activeTab === 'performance' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="border-b-2 py-2 px-1 text-sm font-medium whitespace-nowrap">
                    Performance
                </button>
                <button @click="if (window.ActivityPerformance) window.ActivityPerformance.cleanup(); activeTab = 'details'; $nextTick(() => { if (window.initActivityPagination) window.initActivityPagination(); })"
                        :class="activeTab === 'details' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="border-b-2 py-2 px-1 text-sm font-medium whitespace-nowrap">
                    Details
                </button>
                <button @click="if (window.ActivityPerformance) window.ActivityPerformance.cleanup(); activeTab = 'gps';"
                        :class="activeTab === 'gps' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="border-b-2 py-2 px-1 text-sm font-medium whitespace-nowrap">
                    GPS
                </button>
                <button @click="if (window.ActivityPerformance) window.ActivityPerformance.cleanup(); activeTab = 'weather-history';"
                        :class="activeTab === 'weather-history' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="border-b-2 py-2 px-1 text-sm font-medium whitespace-nowrap">
                    Weather History
                </button>
            </nav>

            <!-- Performance Tab Content -->
            <div class="mt-4" :class="{'hidden': activeTab !== 'performance', 'block': activeTab === 'performance'}">
                {% include 'workorders/activity_performance.html' %}
            </div>

            <!-- Details Tab Content -->
            <div class="mt-4 hidden" :class="{'hidden': activeTab !== 'details', 'block': activeTab === 'details'}">
                {% if activities %}
                {% include 'workorders/activity_details.html' %}
                {% endif %}
            </div>

            <!-- GPS Tab Content -->
            <div class="mt-4 hidden" :class="{'hidden': activeTab !== 'gps', 'block': activeTab === 'gps'}"
                 x-data="gpsTabComponent()"
                 x-show="activeTab === 'gps'"
                 data-workorder-id="{{ workorder_id }}"
                 x-init="$watch('activeTab', value => {
                     if (value === 'gps') {
                         // Initialize vehicle filter if not already done
                         if (!vehiclesLoaded && !loadingVehicles) {
                             loadVehicles();
                         }
                         // If we have cached chart data, check if it needs re-rendering
                         if (selectedVehicle && chartCache.has(selectedVehicle)) {
                             $nextTick(() => {
                                 setTimeout(() => {
                                     // Only re-render if chart container is empty
                                     const chartContainer = document.getElementById('gps-chart-container');
                                     if (chartContainer && chartContainer.innerHTML.trim() === '') {
                                         renderChart();
                                     }
                                 }, 100);
                             });
                         } else {
                             // Add small delay to ensure Alpine.js x-show has fully updated DOM visibility
                             $nextTick(() => {
                                 setTimeout(() => {
                                     if (window.Plotly && document.getElementById('gps-speed-chart')) {
                                         window.Plotly.Plots.resize('gps-speed-chart');
                                     }
                                 }, 100);
                             });
                         }
                     }
                 })"
                <!-- Vehicle Filter -->
                <div class="flex justify-start mb-4">
                    <!-- Single filter container that changes content -->
                    <div class="gps-filter-container">
                        <!-- Loading state -->
                        <div x-show="loadingVehicles" class="gps-loading-state">
                            <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
                            Loading vehicles...
                            <!-- No arrow during loading -->
                        </div>

                        <!-- Vehicle Select (only show when loaded) -->
                        <div x-show="vehiclesLoaded && !loadingVehicles" class="relative">
                            <select id="gps-vehicle-filter"
                                    x-ref="vehicleSelect">
                                <template x-for="vehicle in vehicles" :key="vehicle.id">
                                    <option :value="vehicle.id" x-text="vehicle.name"></option>
                                </template>
                            </select>
                            <!-- External arrow positioned over the TomSelect -->
                            <svg class="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none z-50"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Error state -->
                    <div x-show="vehicleError" class="text-sm text-red-600 ml-4" x-text="vehicleError"></div>
                </div>



                <!-- Dynamic GPS Chart Container -->
                <div x-transition.opacity.duration.200ms>
                    <!-- Loading state for GPS chart -->
                    <div x-show="gpsChartLoading" class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading GPS chart...</p>
                    </div>

                    <!-- Error state for GPS chart -->
                    <div x-show="gpsChartError && !gpsChartLoading" class="py-4 text-center">
                        <p class="text-red-600 text-sm" x-text="gpsChartError"></p>
                    </div>

                    <!-- GPS Chart Content -->
                    <div x-show="selectedVehicle" class="w-full max-w-full overflow-hidden">
                        <div id="gps-chart-container" class="w-full" style="max-width: 100%; overflow: hidden; display: block;">
                            <!-- Chart will be dynamically inserted here -->
                        </div>
                    </div>

                    <!-- Default state when no vehicle is selected -->
                    <div x-show="!selectedVehicle && !gpsChartLoading && !gpsChartError" class="py-4 text-center">
                        <p class="text-gray-500 text-sm">Select a vehicle to view GPS speed data.</p>
                    </div>
                </div>


            </div>

            <!-- Weather History Tab Content -->
            <div class="mt-4 hidden" :class="{'hidden': activeTab !== 'weather-history', 'block': activeTab === 'weather-history'}"
                 x-data="weatherHistoryComponent({{ workorder_id|default:'null' }})"
                 x-show="activeTab === 'weather-history'"
                 x-init="$watch('activeTab', value => {
                     if (value === 'weather-history') {
                         // Load data only if we don't have it and aren't already loading
                         if (!weatherData && !loading) {
                             loadWeatherHistory();
                         } else if (weatherData) {
                             // Re-render chart when tab becomes visible (data already cached)
                             $nextTick(() => {
                                 setTimeout(() => renderWeatherChart(), 100);
                             });
                         }
                     }
                 })"

                <!-- Loading State -->
                <div x-show="loading" class="text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading weather history...</p>
                </div>

                <!-- Error State -->
                <div x-show="error" class="py-4">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-red-600 mr-2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                            </svg>
                            <span class="text-red-800 font-medium">Error loading weather history</span>
                        </div>
                        <p class="text-red-700 mt-1" x-text="error"></p>
                        <button @click="loadWeatherHistory()"
                                class="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors">
                            Retry
                        </button>
                    </div>
                </div>

                <!-- Weather History Chart Container -->
                <div x-show="weatherData && !loading && !error" class="px-5 pb-5 flex flex-col justify-start">
                    <!-- Chart Instructions -->
                    <div class="text-center">
                        <p class="text-xs text-gray-500 font-medium italic">
                            Drag to select time period • Mouse wheel to zoom • Click "Reset Zoom" to return to full view
                        </p>
                    </div>
                    <div class="w-full relative flex-1 flex items-start justify-center pt-2">
                        <canvas id="weather-history-chart" style="display: block;"></canvas>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    {% else %}
    <div class="flex-grow p-6 bg-white rounded-lg shadow-lg">
        <p class="text-center text-gray-600">No activity data available. Please select a Workorder.</p>
    </div>
    {% endif %}

    {% if workorder_id %}

    {% if preset_details_data %}
    {% include 'workorders/presets_details.html' %}
    {% endif %}

    <!-- Video Player Component -->
    {% include 'workorders/video_component.html' %}

    <div class="p-4 bg-white rounded-lg shadow-lg">
        {% if telemetry_chart %}
        <div class="overflow-x-auto w-full max-w-full">
            {{ telemetry_chart|safe }}
        </div>
        {% else %}
        <div class="py-8 text-center">
            <p class="text-gray-500">No telemetry data available for set "{{ full_set_name }}" during this workorder's
                timeframe ({{ telemetry_start_date|date:'Y-m-d' }} to {{ telemetry_end_date|date:'Y-m-d' }}).</p>
        </div>
        {% endif %}
    </div>
    {% endif %}

</div>

